import { Redis } from 'ioredis'
import { REDIS_KEYS } from './redis'
import { QueueManager } from './queue'
import { sendFeedbackNotification } from './firebase'
import { redisClient } from './redis'

export function setupRedisKeyEvents(redisSubscriber: Redis) {
  redisSubscriber.on('message', (channel, message) => {
    // Channel will be like '__keyevent@0__:expired'
    // Message will be the key that expired, e.g., 'gomama:session:expire:4bd697bc-6b90-45cd-b713-44d556c76069'
    console.log(`ℹ️ Redis Key Event: Channel: ${channel}, Message: ${message}`)

    if (channel === `__keyevent@0__:expired`) {
      // Handle expired sessions from SESSION_EXPIRY
      if (message.startsWith(`${REDIS_KEYS.SESSION_EXPIRE}`)) {
        const sessionId = message.split(':')[3] // gomama:session:expire:4bd697bc-6b90-45cd-b713-44d556c76069
        console.log(`✅ Session ${sessionId} expired. Adding to cleanup queue.`)
        QueueManager.addSessionCleanup({ sessionId, reason: 'expired' })

        // Send feedback notification here if it's tied to expiry
        // You'll need to fetch userId from Redis using sessionId
        redisClient.hget(REDIS_KEYS.SESSIONS.BY_USER, sessionId).then((userId) => {
          if (userId) {
            sendFeedbackNotification(sessionId, userId)
          }
        })
      }

      // Handle idle sessions from SESSION_ENTRY_CHECK
      if (message.startsWith(`${REDIS_KEYS.ENTRY_CHECK_EXPIRE}`)) {
        const sessionId = message.split(':')[3] // gomama:session:entry_check_expire:4bd697bc-6b90-45cd-b713-44d556c76069
        console.log(`✅ Session ${sessionId} entry deadline expired. Adding to cleanup queue.`)
        // This might need a different job type or logic if it's not a full cleanup
        // For now, assuming it also leads to a cleanup
        QueueManager.addSessionCleanup({ sessionId, reason: 'idle' })

        // Send feedback notification here if it's tied to idle expiry
        // You'll need to fetch userId from Redis using sessionId
        // redisClient.hget(REDIS_KEYS.SESSIONS.BY_USER, sessionId).then((userId) => {
        //   if (userId) {
        //     sendFeedbackNotification(sessionId, userId)
        //   }
        // })
      }

      // TODO: 5 minutes before SESSION_EXPIRY, send a reminder notification
    }
  })

  // Subscribe to keyspace events for expired keys
  redisSubscriber.subscribe(`__keyevent@0__:expired`)
  console.log('💪 Subscribed to Redis keyspace events for expired keys.')
}
