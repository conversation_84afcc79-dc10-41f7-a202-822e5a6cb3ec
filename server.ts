import express from 'express'
import dotenv from 'dotenv'
import { setupRedis } from './redis'
import { setupFirebase } from './firebase'
import { setupRedisKeyEvents } from './events'
import { setupMonitoring, getSystemMetrics } from './monitoring'
import { QueueManager } from './queue'
import { setupMQTT, getMQTTClient } from './mqtt'
import { validateMQTTCredentials, checkTopicPermission } from './mqtt-auth'
import { createWorkers } from './workers'

dotenv.config()
const port = Number(process.env.PORT || 9001)
const redisUrl = `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`

async function startServer() {
  // Initialize monitoring
  setupMonitoring()

  // Setup Redis client with keyspace events
  const { redisClient, redisSubscriber } = await setupRedis(redisUrl)

  // Setup MQTT client
  const mqttClient = setupMQTT()

  // Wait for MQTT connection before proceeding
  await new Promise<void>((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('MQTT connection timeout'))
    }, 30000) // 30 second timeout

    mqttClient.once('connected', () => {
      clearTimeout(timeout)
      resolve()
    })

    mqttClient.once('error', (error) => {
      clearTimeout(timeout)
      reject(error)
    })
  })

  // Setup Firebase and Redis key events
  setupFirebase(redisClient)
  setupRedisKeyEvents(redisSubscriber)

  // Start BullMQ workers
  createWorkers(redisClient)

  const app = express()

  // Middleware for parsing JSON
  app.use(express.json())

  // Add a metrics endpoint
  app.get('/metrics', async (req, res) => {
    const metricsApiKey = process.env.METRICS_API_KEY
    const authHeader = req.headers.authorization
    if (!metricsApiKey || authHeader !== `Bearer ${metricsApiKey}`) {
      res.status(401).json({ error: 'Unauthorized' })
      return
    }

    const systemMetrics = getSystemMetrics()
    const queueStats = await QueueManager.getQueueStats()
    const mqttClient = getMQTTClient()

    const metrics = {
      ...systemMetrics,
      queues: queueStats,
      mqtt: {
        connected: mqttClient?.isClientConnected() || false,
        clientId: process.env.MQTT_CLIENT_ID
      },
      status: 'running',
      timestamp: Date.now()
    }

    res.json(metrics)
  })

  // MQTT Authentication endpoints
  app.post('/mqtt/auth', (req, res) => {
    try {
      const { clientid, username, password, peerhost } = req.body
      const result = validateMQTTCredentials(username, password)

      if (result.valid) {
        res.status(200).send('allow')
      } else {
        res.status(401).send('deny')
      }
    } catch (error) {
      res.status(400).send('deny')
    }
  })

  app.post('/mqtt/superuser', (req, res) => {
    try {
      const { clientid, username, peerhost } = req.body
      // Only realtime and admin clients are superusers
      const isSuperuser = username.startsWith('admin_') || username.startsWith('realtime_')
      res.status(200).send(isSuperuser ? 'allow' : 'deny')
    } catch (error) {
      res.status(400).send('deny')
    }
  })

  app.post('/mqtt/acl', (req, res) => {
    try {
      const { access, username, clientid, topic } = req.body

      // Extract token from username (format: clienttype_userid)
      const parts = username.split('_')
      if (parts.length < 2) {
        res.status(401).send('deny')
        return
      }

      // For now, allow all authenticated users (token validation was done in auth step)
      // In production, implement proper topic-based ACL using checkTopicPermission
      const action = access === '1' ? 'subscribe' : 'publish'
      // const isAllowed = checkTopicPermission(token, topic, action)
      res.status(200).send('allow')
    } catch (error) {
      res.status(400).send('deny')
    }
  })

  // Default route
  app.get('/', (req, res) => {
    res.send('GoMama Realtime Server - MQTT Only')
  })

  // Start the server
  const server = app.listen(port, () => {
    console.log('Listening to port ' + port)
  })
}

startServer().catch(console.error)
