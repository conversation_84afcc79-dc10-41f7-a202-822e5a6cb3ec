import { initializeApp, cert } from 'firebase-admin/app'
import { getMessaging } from 'firebase-admin/messaging'
import { getFirestore } from 'firebase-admin/firestore'
import { Redis } from 'ioredis'
import { REDIS_KEYS, scanAllKeys } from './redis'
import { QueueManager } from './queue'
import { getMQTTClient, MQTTTopicBuilder } from './mqtt'

let redisClientInstance: Redis | null = null

// Throttling and debouncing configuration
const PROCESSING_DEBOUNCE_MS = 500 // Debounce time for processing changes
const MAX_BATCH_SIZE = 50 // Maximum number of documents to process in a batch
const DOCUMENT_CACHE_TTL = 5000 // Time to cache document status (5 seconds)

// Fields we care about for status changes
const TRACKED_FIELDS = ['is_disinfecting', 'is_occupied', 'is_uvc_lamp_on']

// Cache for document status to prevent redundant processing
const documentStatusCache = new Map<string, { status: string; timestamp: number; lastData?: any }>()

// Queue for pending document changes
let pendingChanges: Array<{ docData: any; id: string; timestamp: number }> = []
let processingTimeout: NodeJS.Timeout | null = null

// Store active listeners to manage them
const activeListeners: Array<() => void> = []

export function setupFirebase(redisClient: Redis) {
  redisClientInstance = redisClient

  const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH || './serviceAccountKey.json'

  initializeApp({
    credential: cert(serviceAccountPath),
    databaseURL: process.env.FIREBASE_URL
  })

  const db = getFirestore()
  const listingsCollection = db.collection('listings')

  console.log('💪 Starting true field-specific Firestore observers...')

  // Set up field-specific listeners
  setupTrueFieldSpecificListeners(listingsCollection, redisClient)

  // Emit all statuses to MQTT on startup
  emitAllStatusesToMQTT(redisClient).catch(console.error)
}

function setupTrueFieldSpecificListeners(
  collection: FirebaseFirestore.CollectionReference,
  redisClient: Redis
) {
  // First, get all existing documents to set up individual listeners
  // The Admin SDK doesn't support select(), so we'll get all documents and filter fields ourselves
  collection
    .get()
    .then((snapshot) => {
      console.log(`💪 Setting up listeners for ${snapshot.size} listings`)

      // Set up a listener for each document
      snapshot.forEach((doc) => {
        setupDocumentListener(doc.ref, redisClient)
      })

      // Also listen for new documents being added to the collection
      const collectionListener = collection.onSnapshot(
        (snapshot) => {
          snapshot.docChanges().forEach((change) => {
            if (change.type === 'added') {
              console.log(`🆕 New listing added: ${change.doc.id}, setting up listener`)
              setupDocumentListener(change.doc.ref, redisClient)
            }
          })
        },
        (error) => {
          console.error('🚨 Error listening for new listings:', error)
        }
      )

      // Add to active listeners
      activeListeners.push(() => collectionListener())
    })
    .catch((error) => {
      console.error('🚨 Error getting initial listings:', error)
    })
}

function setupDocumentListener(docRef: FirebaseFirestore.DocumentReference, redisClient: Redis) {
  // Create a listener that only triggers when specific fields change
  // The Admin SDK doesn't support select(), so we'll use a regular listener
  const unsubscribe = docRef.onSnapshot(
    (doc) => {
      if (!doc.exists) return

      const docId = doc.id

      // Get all the data (Admin SDK doesn't support field selection in the query)
      const fullDocData = doc.data()

      if (!fullDocData) return

      // Extract only the fields we care about to minimize processing
      const docData: any = {}
      TRACKED_FIELDS.forEach((field) => {
        if (field in fullDocData) {
          docData[field] = fullDocData[field]
        }
      })

      // Log what fields we're receiving for debugging
      // console.log(`Document update for ${docId}, received all fields:`, Object.keys(fullDocData))
      // console.log(`Document update for ${docId}, filtered to fields:`, Object.keys(docData))

      // Extract only the fields we need
      const relevantData = {
        id: docId,
        is_disinfecting: docData.is_disinfecting,
        is_occupied: docData.is_occupied,
        is_uvc_lamp_on: docData.is_uvc_lamp_on
      }

      if (docId == '2205071905229751224') {
        console.log(`Document update for ${docId}, received fields:`, relevantData)
      }

      // Check if any tracked fields have changed
      const cachedDoc = documentStatusCache.get(docId)
      const hasTrackedFieldChanges = TRACKED_FIELDS.some((field) => {
        // If we have cached data, check if the field has changed
        if (cachedDoc?.lastData) {
          if (docId == '2205071905229751224') {
            console.log(`Document update for ${docId}, cached data:`, cachedDoc.lastData)
          }

          return docData[field] !== cachedDoc.lastData[field]
        }

        if (docId == '2205071905229751224') {
          console.log(`Document update for ${docId}, no cached data, considering change`)
        }

        // If no cached data, consider it a change
        return field in docData
      })

      if (hasTrackedFieldChanges) {
        if (docId == '2205071905229751224') {
          console.log(`Document update for ${docId}, tracked field changes detected`)
        }

        // Update cache with the latest field values
        if (cachedDoc) {
          cachedDoc.lastData = { ...relevantData }
        } else {
          documentStatusCache.set(docId, {
            status: 'unknown',
            timestamp: Date.now(),
            lastData: { ...relevantData }
          })
        }

        if (docId == '2205071905229751224') {
          console.log(`Document update for ${docId}, adding to pending changes queue`)
        }

        // Add to pending changes queue
        pendingChanges.push({
          docData: relevantData,
          id: docId,
          timestamp: Date.now()
        })

        // Debounce processing
        if (processingTimeout) {
          clearTimeout(processingTimeout)
        }

        processingTimeout = setTimeout(() => {
          processBatchedChanges(redisClient)
        }, PROCESSING_DEBOUNCE_MS)
      } else {
        if (docId == '2205071905229751224') {
          console.log(`Document update for ${docId}, no tracked field changes detected`)
        }
      }
    },
    (error) => {
      console.error(`🚨 Error listening to document ${docRef.id}:`, error)
    }
  )

  // Add to active listeners
  activeListeners.push(unsubscribe)
}

// Process batched document changes
async function processBatchedChanges(redisClient: Redis) {
  if (pendingChanges.length === 0) return

  console.log(`⌨️ Processing batch of ${pendingChanges.length} document changes`)

  // Take a batch of changes (up to MAX_BATCH_SIZE)
  const batch = pendingChanges.slice(0, MAX_BATCH_SIZE)
  pendingChanges = pendingChanges.slice(MAX_BATCH_SIZE)

  // Create a single pipeline for all Redis operations
  const pipeline = redisClient.pipeline()
  const statusChanges: Array<{ id: string; status: string; previousStatus: string | null }> = []

  try {
    // First pass: determine status changes and build status lookup
    const statusLookupPromises = batch.map(async ({ docData, id }) => {
      const isDisinfecting = docData.is_disinfecting
      const isOccupied = docData.is_occupied
      const isUvcLampOn = docData.is_uvc_lamp_on
      let status: 'idle' | 'disinfecting' | 'occupied' = 'idle'

      if (isOccupied) {
        status = 'occupied'
      } else if (isDisinfecting || isUvcLampOn) {
        status = 'disinfecting'
      } else {
        status = 'idle'
      }

      if (docData.id == '2205071905229751224') {
        console.log(
          `Processing batched changes for ${docData.id}, isDisinfecting: ${isDisinfecting}, isOccupied: ${isOccupied}, isUvcLampOn: ${isUvcLampOn}`
        )
        console.log(`Processing batched changes for ${docData.id}, determined status: ${status}`)
        console.log(
          `Processing batched changes for ${docData.id}, cached data:`,
          documentStatusCache.get(docData.id)
        )
      }

      // Check cache first to avoid Redis calls for unchanged status
      const cachedDoc = documentStatusCache.get(id)
      if (
        cachedDoc &&
        cachedDoc.status === status &&
        Date.now() - cachedDoc.timestamp < DOCUMENT_CACHE_TTL
      ) {
        if (docData.id == '2205071905229751224') {
          console.log(`Processing batched changes for ${docData.id}, skipping unchanged status`)
        }

        return null // Skip unchanged status within cache TTL
      }

      // Get previous status from Redis
      const previousStatus = await redisClient.hget(REDIS_KEYS.LISTINGS.ACTIVE, id)

      // Only process if status has changed
      if (status !== previousStatus) {
        // Update cache
        if (cachedDoc) {
          cachedDoc.status = status
          cachedDoc.timestamp = Date.now()
        } else {
          documentStatusCache.set(id, { status, timestamp: Date.now() })
        }

        // Add to status changes for processing
        statusChanges.push({ id, status, previousStatus })

        // Update listing status in Redis
        pipeline.hset(REDIS_KEYS.LISTINGS.ACTIVE, id, status)
      }

      return { id, status, previousStatus }
    })

    // Wait for all status lookups to complete
    await Promise.all(statusLookupPromises)

    // If no status changes, skip further processing
    if (statusChanges.length === 0) {
      console.log('✅ No status changes detected in batch, skipping this batch')

      if (pendingChanges.length > 0) {
        processingTimeout = setTimeout(() => {
          processBatchedChanges(redisClient)
        }, PROCESSING_DEBOUNCE_MS)
      } else {
        processingTimeout = null
      }

      return
    }

    console.log(`⌨️ Processing ${statusChanges.length} actual status changes`)

    // Second pass: handle session operations for changed listings
    // Get all sessions in one batch to minimize Redis calls
    const sessionKeys = await scanAllKeys(`${REDIS_KEYS.SESSIONS.ACTIVE}:*`)

    if (sessionKeys.length > 0) {
      // Get all sessions in a single batch operation
      const sessionPromises = sessionKeys.map((key) => redisClient.hgetall(key))
      const sessions = await Promise.all(sessionPromises)

      // Process each status change
      for (const { id, status } of statusChanges) {
        // Find sessions matching this listing
        const matchingSessions = sessions.filter(
          (session) => session && session.firestore_id === id
        )

        // Handle non-idle status (clear entry checks)
        if (status !== 'idle' && matchingSessions.length > 0) {
          for (const session of matchingSessions) {
            pipeline.zrem(REDIS_KEYS.SESSIONS.ENTRY_CHECK, session.id) // redis map for entry checks
            pipeline.del(`${REDIS_KEYS.ENTRY_CHECK_EXPIRE}:${session.id}`) // redis key with ttl for entry expiry
          }
        }

        // Handle disinfecting status (terminate sessions)
        if (status === 'disinfecting' && matchingSessions.length > 0) {
          console.log('⌛ Terminating sessions for listing, halfway disinfecting session', id)

          for (const session of matchingSessions) {
            const userId = await redisClient.hget(REDIS_KEYS.SESSIONS.BY_USER, session.id)

            if (userId) {
              console.log(
                '⌛ Ending the session due to user leaving halfway and the pod is now under disinfecting process',
                userId
              )

              await QueueManager.addSessionCleanup({
                sessionId: session.id,
                userId,
                reason: 'terminated'
              })

              // Schedule feedback notification using BullMQ
              setTimeout(async () => {
                const devicesJson = await redisClientInstance?.hget(
                  REDIS_KEYS.USERS.DEVICES,
                  userId
                )
                const devices = devicesJson ? JSON.parse(devicesJson) : []

                if (devices.length > 0) {
                  await QueueManager.addNotification({
                    type: 'feedback',
                    userId,
                    sessionId: session.id,
                    message: {
                      title: 'Have you submitted the feedback for your last session yet?',
                      body: 'Thank you for using GoMama Pod! Please leave feedback about the pod to help other Mamas.',
                      data: { session_id: session.id, type: 'reminder' }
                    },
                    devices
                  })
                }
              }, 100)
            }
          }
        }
      }
    }

    // Execute all Redis operations in a single pipeline
    await pipeline.exec()

    // After pipeline execution, publish status changes via MQTT
    const mqttClient = getMQTTClient()
    if (mqttClient && mqttClient.isClientConnected()) {
      for (const { id, status } of statusChanges) {
        await mqttClient.publish(
          MQTTTopicBuilder.listingStatus(id),
          {
            timestamp: Date.now(),
            source: 'realtime',
            type: 'listing_status_update',
            data: { listingId: id, status }
          },
          { qos: 1, retain: true }
        )
      }
      console.log(`✅ Published ${statusChanges.length} listing status changes via MQTT`)
    } else {
      console.warn('⚠️ MQTT client not available, status changes not published')
    }
  } catch (error) {
    console.error('🚨 Error processing batch of document changes:', error)
  }

  // If there are more pending changes, process the next batch
  if (pendingChanges.length > 0) {
    processingTimeout = setTimeout(() => {
      processBatchedChanges(redisClient)
    }, PROCESSING_DEBOUNCE_MS)
  } else {
    processingTimeout = null
  }
}

async function emitAllStatusesToMQTT(redisClient: Redis) {
  const db = getFirestore()
  const listingsSnapshot = await db.collection('listings').get()
  const mqttClient = getMQTTClient()

  if (!mqttClient || !mqttClient.isClientConnected()) {
    console.warn('⚠️ MQTT client not available, cannot emit all statuses')
    return
  }

  for (const doc of listingsSnapshot.docs) {
    const id = doc.id
    const data = doc.data()
    // Use the same logic as in processBatchedChanges to determine status
    const isDisinfecting = data.is_disinfecting
    const isOccupied = data.is_occupied
    const isUvcLampOn = data.is_uvc_lamp_on
    let status: 'idle' | 'disinfecting' | 'occupied' = 'idle'

    if (isOccupied) {
      status = 'occupied'
    } else if (isDisinfecting || isUvcLampOn) {
      status = 'disinfecting'
    }

    await mqttClient.publish(
      MQTTTopicBuilder.listingStatus(id),
      {
        timestamp: Date.now(),
        source: 'realtime',
        type: 'listing_status_update',
        data: { listingId: id, status }
      },
      { qos: 1, retain: true }
    )
  }

  console.log(`✅ Published all listing statuses to MQTT on startup`)
}

const FEEDBACK_NOTIFICATION_QUEUE = 'feedback_notification_queue'
const FEEDBACK_NOTIFICATION_PROCESSING = 'feedback_notification_processing'

export async function sendFeedbackNotification(sessionId: string, userId: string) {
  if (!redisClientInstance) {
    throw new Error('Redis client not initialized')
  }

  // get all devices for this user
  const devicesJson = await redisClientInstance.hget(REDIS_KEYS.USERS.DEVICES, userId)
  const devices = devicesJson ? JSON.parse(devicesJson) : []

  const message = {
    notification: {
      title: '"Have you submitted the feedback for your last session yet?',
      body: 'Thank you for using GoMama Pod! Please leave feedback about the pod to help other Mamas.'
    },
    data: { session_id: sessionId, type: 'reminder' },
    tokens: devices
  }

  // Add to Redis queue with initial retry count
  await redisClientInstance.rpush(
    FEEDBACK_NOTIFICATION_QUEUE,
    JSON.stringify({ message, timestamp: Date.now(), retryCount: 0 })
  )

  // once we add to queue, remove the device id map
  await redisClientInstance.hdel(REDIS_KEYS.USERS.DEVICES, userId)

  // Process the notification queue immediately instead of waiting for the interval
  try {
    await processFeedbackNotificationQueue()
  } catch (e) {
    console.log('🚨 Error processing feedback notification queue:', e)
  }
}

export async function processFeedbackNotificationQueue() {
  if (!redisClientInstance) {
    throw new Error('Redis client not initialized')
  }

  const processing = await redisClientInstance.get(FEEDBACK_NOTIFICATION_PROCESSING)
  if (processing === '1') {
    console.log('⚠️ Another process is already processing the feedback notification queue')
    return
  }

  try {
    // Set processing flag with 5-minute expiry (in case of crashes)
    await redisClientInstance.set(FEEDBACK_NOTIFICATION_PROCESSING, '1', 'EX', 300)

    // Get the length of the queue
    const queueLength = await redisClientInstance.llen(FEEDBACK_NOTIFICATION_QUEUE)
    if (queueLength === 0) {
      console.log('✅ No feedback notifications to process')
      return
    }

    console.log(`⌨️ Processing ${queueLength} feedback notifications`)

    // Process up to 10 notifications at a time
    const batchSize = Math.min(queueLength, 10)
    for (let i = 0; i < batchSize; i++) {
      const item = await redisClientInstance.lpop(FEEDBACK_NOTIFICATION_QUEUE)
      if (!item) continue

      try {
        const { message, timestamp, retryCount } = JSON.parse(item)

        // Skip if message is too old (more than 24 hours)
        const age = Date.now() - timestamp
        if (age > 24 * 60 * 60 * 1000) {
          console.log('✅ Skipping old feedback notification', message.data.session_id)
          continue
        }

        // Send the notification
        const messaging = getMessaging()
        await messaging.sendEachForMulticast(message)
        console.log('✅ Sent feedback notification for session', message.data.session_id)
      } catch (error) {
        console.error('🚨 Error processing feedback notification:', error)

        // Parse the item again
        const parsedItem = JSON.parse(item)

        // Increment retry count and push back to queue if under retry limit
        if (parsedItem.retryCount < 3) {
          parsedItem.retryCount++
          await redisClientInstance.rpush(FEEDBACK_NOTIFICATION_QUEUE, JSON.stringify(parsedItem))

          // If we had an error, process the queue again after a short delay
          // This helps with transient errors but avoids tight loops
          setTimeout(async () => {
            try {
              await processFeedbackNotificationQueue()
            } catch (e) {
              console.log('🚨 Error in delayed queue processing:', e)
            }
          }, 10000) // Wait 10 seconds before trying again
        } else {
          console.error('🚨 Exceeded retry limit for feedback notification:', parsedItem)
        }
      }
    }

    // If there are more items in the queue after processing this batch,
    // continue processing immediately
    if (queueLength > batchSize) {
      setImmediate(async () => {
        try {
          await processFeedbackNotificationQueue()
        } catch (e) {
          console.log('🚨 Error in continued queue processing:', e)
        }
      })
    }
  } finally {
    // Clear processing flag
    await redisClientInstance.del(FEEDBACK_NOTIFICATION_PROCESSING)
  }
}
