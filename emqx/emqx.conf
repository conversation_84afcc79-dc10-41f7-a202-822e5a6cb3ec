##--------------------------------------------------------------------
## EMQX Configuration
##--------------------------------------------------------------------

## Node name
node.name = gomama_emqx@127.0.0.1
node.cookie = gomama_emqx_cookie_2025

## Node data directory
node.data_dir = "/opt/emqx/data"

##--------------------------------------------------------------------
## Cluster
##--------------------------------------------------------------------
cluster {
  discovery_strategy = manual
}

##--------------------------------------------------------------------
## Listeners
##--------------------------------------------------------------------
listeners.tcp.default {
  bind = "0.0.0.0:1883"
  max_connections = 1024000
  max_conn_rate = 1000
}

listeners.ssl.default {
  bind = "0.0.0.0:8883"
  max_connections = 512000
  max_conn_rate = 500
  ssl_options = {
    keyfile = "/etc/emqx/certs/key.pem"
    certfile = "/etc/emqx/certs/cert.pem"
  }
}

listeners.ws.default {
  bind = "0.0.0.0:8083"
  max_connections = 102400
  max_conn_rate = 1000
}

listeners.wss.default {
  bind = "0.0.0.0:8084"
  max_connections = 102400
  max_conn_rate = 1000
}

##--------------------------------------------------------------------
## Authentication
##--------------------------------------------------------------------
authentication = [
  # for servers
  {
    password_hash_algorithm = {
      name = "bcrypt"
      salt_rounds = 10
    }
    mechanism = "password_based"
    backend = "built_in_database"
    user_id_type = "username"
    bootstrap_file = "/opt/emqx/etc/auth-built-in-db-bootstrap.csv"
    bootstrap_type = "plain"
    precondition = ""
  },
  # for mobile clients
  {
    use_jwks = false
    algorithm = "hmac-based"
    secret = "L1AIwYY5V2KqFOxG2pJ3hKgz0zD2Gy0G" # TODO: use env
    secret_base64_encoded = false
    mechanism = "jwt"
    acl_claim_name = "acl"
    verify_claims = { 
      username = "${username}"
    }
    disconnect_after_expire = true
    from = "password"
  }
]

##--------------------------------------------------------------------
## Authorization (ACL)
##--------------------------------------------------------------------
authorization {
  sources = [
    # {
    #   type = http
    #   enable = true

    #   method = post
    #   url = "http://127.0.0.1:3334/mqtt/acl"
    #   headers {
    #     "Content-Type" = "application/json"
    #   }
    #   body {
    #     username = "${username}"
    #     clientid = "${clientid}"
    #     access = "${access}"
    #     topic = "${topic}"
    #     ipaddr = "${peerhost}"
    #   }

    #   request_timeout = 5s
    #   connect_timeout = 5s
    #   pool_size = 32
    #   # pool_type = random <-- REMOVE THIS LINE
    #   # If you encounter issues, also check if 'pool_size' is supported directly here,
    #   # as sometimes it's handled by a separate resource configuration for HTTP clients.

    #   # SSL verification (if using HTTPS)
    #   ssl {
    #     enable = false
    #     verify = verify_none
    #   }
    # }
  ]

  # Deny by default if no rules match
  no_match = deny

  # Cache settings for better performance
  cache {
    enable = true
    max_size = 32
    ttl = 1m
  }
}

##--------------------------------------------------------------------
## MQTT Settings
##--------------------------------------------------------------------
mqtt {
  max_packet_size = 1MB
  max_clientid_len = 65535
  max_topic_levels = 128
  max_qos_allowed = 2
  max_topic_alias = 65535
  retain_available = true
  wildcard_subscription = true
  shared_subscription = true
  exclusive_subscription = false
}

##--------------------------------------------------------------------
## Logging
##--------------------------------------------------------------------
log {
  console_handler {
    enable = true
    level = warning
    formatter = text
    time_offset = system
  }
  
  file_handlers.default {
    enable = true
    level = warning
    file = "log/emqx.log"
    rotation {
      enable = true
      size = 50MB
      count = 10
    }
    formatter = text
    time_offset = system
  }
}

##--------------------------------------------------------------------
## Dashboard
##--------------------------------------------------------------------
dashboard {
  listeners.http {
    bind = "0.0.0.0:18083"
    max_connections = 512
    backlog = 1024
    send_timeout = 10s
    inet6 = false
    ipv6_v6only = false
  }
  
  default_username = admin
  default_password = gomama2024
  token_expired_time = 60m
  cors = false
}

##--------------------------------------------------------------------
## API & Management
##--------------------------------------------------------------------


##--------------------------------------------------------------------
## Zones
##--------------------------------------------------------------------


##--------------------------------------------------------------------
## Plugins
##--------------------------------------------------------------------
plugins {
  states = []
}