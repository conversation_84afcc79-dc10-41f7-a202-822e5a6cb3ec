import Redis from 'ioredis'

// Improved Redis key structure with consistent naming and clear organization
export const REDIS_KEYS = {
  // Listings - Hash structure for efficient lookups
  LISTINGS: {
    ACTIVE: 'gomama:listings:active', // Hash: {listing_id: status}
    STATUS_HISTORY: 'gomama:listings:history' // Hash: {listing_id: json_history}
  },

  // Sessions - Organized by data type
  SESSIONS: {
    ACTIVE: 'gomama:sessions:active', // Hash: {session_id: json_data}
    BY_USER: 'gomama:sessions:by_user', // Hash: {user_id: session_id}
    EXPIRY: 'gomama:sessions:expiry', // Sorted Set: {session_id: expiry_timestamp}
    ENTRY_CHECK: 'gomama:sessions:entry_check' // Sorted Set: {session_id: deadline_timestamp}
  },

  // Users & Devices
  USERS: {
    DEVICES: 'gomama:users:devices', // Hash: {user_id: json_device_array}
    SESSIONS: 'gomama:users:sessions' // Hash: {user_id: current_session_id}
  },

  // System keys
  SYSTEM: {
    METRICS: 'gomama:system:metrics',
    HEALTH: 'gomama:system:health'
  },

  // Redis key prefixes for expiring dummy keys
  SESSION_EXPIRE: 'gomama:session:expire',
  ENTRY_CHECK_EXPIRE: 'gomama:session:entry_check_expire'
} as const

// Helper functions for key construction
export const RedisKeyBuilder = {
  // Session keys
  sessionByUser: (userId: string) => `${REDIS_KEYS.SESSIONS.BY_USER}:${userId}`, // Used for user-to-session mapping

  // User keys
  userDevices: (userId: string) => `${REDIS_KEYS.USERS.DEVICES}:${userId}`, // Used for device management

  // Expiry keys (for Redis keyspace events)
  sessionExpiry: (sessionId: string) => `${REDIS_KEYS.SESSIONS.EXPIRY}:${sessionId}`, // Used for session expiry tracking
  sessionEntryCheck: (sessionId: string) => `${REDIS_KEYS.SESSIONS.ENTRY_CHECK}:${sessionId}` // Used for entry deadline tracking
} as const

export let redisClient: Redis

export async function setupRedis(redisUrl: string) {
  redisClient = new Redis(redisUrl, {
    maxRetriesPerRequest: null // needed for bullmq
  })
  const redisSubscriber = redisClient.duplicate()

  // Enable keyspace events for expired keys
  await redisClient.config('SET', 'notify-keyspace-events', 'Ex')
  console.log('💪 Redis client connected with keyspace events enabled')

  return { redisClient, redisSubscriber }
}

export async function scanAllKeys(pattern: string): Promise<string[]> {
  const keys: string[] = []
  let cursor = '0'

  do {
    const [nextCursor, elements] = await redisClient.scan(cursor, 'MATCH', pattern)
    keys.push(...elements)
    cursor = nextCursor
  } while (cursor !== '0')

  return keys
}
