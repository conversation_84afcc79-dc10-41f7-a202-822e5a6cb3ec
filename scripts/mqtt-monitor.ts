#!/usr/bin/env tsx

/**
 * MQTT System Monitoring Dashboard
 * Real-time monitoring of MQTT broker health, connections, and message flow
 */

import mqtt, { MqttClient } from 'mqtt'
import { generateRealtimeCredentials } from '../mqtt-auth'
import { performance } from 'perf_hooks'

interface MonitoringStats {
  timestamp: number
  connections: {
    total: number
    active: number
    failed: number
  }
  messages: {
    published: number
    received: number
    retained: number
    qos0: number
    qos1: number
    qos2: number
  }
  topics: {
    [topic: string]: {
      subscribers: number
      messages: number
      lastActivity: number
    }
  }
  performance: {
    avgLatency: number
    maxLatency: number
    minLatency: number
    throughput: number
  }
  errors: string[]
}

class MQTTMonitor {
  private client: MqttClient | null = null
  private stats: MonitoringStats
  private isRunning = false
  private startTime: number
  private messageLatencies: number[] = []
  private topicActivity: Map<string, { count: number; lastSeen: number }> = new Map()

  constructor(private brokerUrl: string = 'mqtt://localhost:1883') {
    this.startTime = Date.now()
    this.stats = this.initializeStats()
  }

  private initializeStats(): MonitoringStats {
    return {
      timestamp: Date.now(),
      connections: {
        total: 0,
        active: 0,
        failed: 0
      },
      messages: {
        published: 0,
        received: 0,
        retained: 0,
        qos0: 0,
        qos1: 0,
        qos2: 0
      },
      topics: {},
      performance: {
        avgLatency: 0,
        maxLatency: 0,
        minLatency: Infinity,
        throughput: 0
      },
      errors: []
    }
  }

  async start(): Promise<void> {
    console.log('🔍 Starting MQTT Monitor...')

    try {
      const credentials = generateRealtimeCredentials('mqtt_monitor')

      this.client = mqtt.connect(this.brokerUrl, {
        clientId: `mqtt_monitor_${Date.now()}`,
        username: credentials.username,
        password: credentials.password,
        keepalive: 30
      })

      this.setupEventHandlers()
      this.isRunning = true

      // Start monitoring loops
      this.startPerformanceMonitoring()
      this.startStatsDisplay()

      console.log('✅ MQTT Monitor started successfully')
      console.log('Press Ctrl+C to stop monitoring\n')
    } catch (error) {
      console.error('❌ Failed to start MQTT Monitor:', error)
      throw error
    }
  }

  private setupEventHandlers(): void {
    if (!this.client) return

    this.client.on('connect', () => {
      console.log('✅ Monitor connected to MQTT broker')
      this.stats.connections.active++

      // Subscribe to all topics for monitoring
      this.client!.subscribe('gomama/+/+/+', { qos: 0 })
      this.client!.subscribe('$SYS/+', { qos: 0 }) // System topics if available
    })

    this.client.on('message', (topic, payload, packet) => {
      this.handleMessage(topic, payload, packet)
    })

    this.client.on('error', (error) => {
      console.error('❌ MQTT Monitor error:', error)
      this.stats.connections.failed++
      this.stats.errors.push(`${new Date().toISOString()}: ${error.message}`)

      // Keep only last 10 errors
      if (this.stats.errors.length > 10) {
        this.stats.errors = this.stats.errors.slice(-10)
      }
    })

    this.client.on('disconnect', () => {
      console.log('❌ Monitor disconnected from MQTT broker')
      this.stats.connections.active--
    })
  }

  private handleMessage(topic: string, payload: Buffer, packet: any): void {
    this.stats.messages.received++

    // Track QoS levels
    switch (packet.qos) {
      case 0:
        this.stats.messages.qos0++
        break
      case 1:
        this.stats.messages.qos1++
        break
      case 2:
        this.stats.messages.qos2++
        break
    }

    // Track retained messages
    if (packet.retain) {
      this.stats.messages.retained++
    }

    // Track topic activity
    const activity = this.topicActivity.get(topic) || { count: 0, lastSeen: 0 }
    activity.count++
    activity.lastSeen = Date.now()
    this.topicActivity.set(topic, activity)

    // Update topic stats
    if (!this.stats.topics[topic]) {
      this.stats.topics[topic] = {
        subscribers: 0,
        messages: 0,
        lastActivity: 0
      }
    }
    this.stats.topics[topic].messages++
    this.stats.topics[topic].lastActivity = Date.now()

    // Calculate latency if message has timestamp
    try {
      const message = JSON.parse(payload.toString())
      if (message.timestamp) {
        const latency = Date.now() - message.timestamp
        this.messageLatencies.push(latency)

        // Keep only last 100 latencies for calculation
        if (this.messageLatencies.length > 100) {
          this.messageLatencies = this.messageLatencies.slice(-100)
        }

        this.updateLatencyStats(latency)
      }
    } catch {
      // Ignore non-JSON messages
    }
  }

  private updateLatencyStats(latency: number): void {
    this.stats.performance.maxLatency = Math.max(this.stats.performance.maxLatency, latency)
    this.stats.performance.minLatency = Math.min(this.stats.performance.minLatency, latency)

    if (this.messageLatencies.length > 0) {
      this.stats.performance.avgLatency =
        this.messageLatencies.reduce((sum, l) => sum + l, 0) / this.messageLatencies.length
    }
  }

  private startPerformanceMonitoring(): void {
    setInterval(() => {
      if (!this.isRunning) return

      // Calculate throughput (messages per second)
      const runtime = (Date.now() - this.startTime) / 1000
      this.stats.performance.throughput = this.stats.messages.received / runtime

      // Update timestamp
      this.stats.timestamp = Date.now()
    }, 1000)
  }

  private startStatsDisplay(): void {
    setInterval(() => {
      if (!this.isRunning) return
      this.displayStats()
    }, 5000) // Update display every 5 seconds
  }

  private displayStats(): void {
    // Clear screen
    console.clear()

    const runtime = Math.floor((Date.now() - this.startTime) / 1000)
    const hours = Math.floor(runtime / 3600)
    const minutes = Math.floor((runtime % 3600) / 60)
    const seconds = runtime % 60

    console.log('📊 MQTT System Monitor Dashboard')
    console.log('='.repeat(60))
    console.log(
      `🕐 Runtime: ${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    )
    console.log(`🔗 Broker: ${this.brokerUrl}`)
    console.log(`📅 Last Update: ${new Date().toLocaleTimeString()}`)
    console.log()

    // Connection Stats
    console.log('🔌 Connection Statistics:')
    console.log(`   Active Connections: ${this.stats.connections.active}`)
    console.log(`   Failed Connections: ${this.stats.connections.failed}`)
    console.log()

    // Message Stats
    console.log('📨 Message Statistics:')
    console.log(`   Total Received: ${this.stats.messages.received.toLocaleString()}`)
    console.log(`   QoS 0: ${this.stats.messages.qos0.toLocaleString()}`)
    console.log(`   QoS 1: ${this.stats.messages.qos1.toLocaleString()}`)
    console.log(`   QoS 2: ${this.stats.messages.qos2.toLocaleString()}`)
    console.log(`   Retained: ${this.stats.messages.retained.toLocaleString()}`)
    console.log()

    // Performance Stats
    console.log('⚡ Performance Statistics:')
    console.log(`   Throughput: ${this.stats.performance.throughput.toFixed(2)} msg/sec`)
    if (this.messageLatencies.length > 0) {
      console.log(`   Avg Latency: ${this.stats.performance.avgLatency.toFixed(2)}ms`)
      console.log(`   Min Latency: ${this.stats.performance.minLatency.toFixed(2)}ms`)
      console.log(`   Max Latency: ${this.stats.performance.maxLatency.toFixed(2)}ms`)
    }
    console.log()

    // Top Active Topics
    console.log('🔥 Top Active Topics:')
    const sortedTopics = Array.from(this.topicActivity.entries())
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 10)

    if (sortedTopics.length > 0) {
      sortedTopics.forEach(([topic, activity], index) => {
        const timeSinceLastActivity = Math.floor((Date.now() - activity.lastSeen) / 1000)
        console.log(`   ${(index + 1).toString().padStart(2, ' ')}. ${topic}`)
        console.log(
          `       Messages: ${activity.count.toLocaleString()}, Last: ${timeSinceLastActivity}s ago`
        )
      })
    } else {
      console.log('   No topic activity detected')
    }
    console.log()

    // Recent Errors
    if (this.stats.errors.length > 0) {
      console.log('❌ Recent Errors:')
      this.stats.errors.slice(-5).forEach((error) => {
        console.log(`   ${error}`)
      })
      console.log()
    }

    // Health Status
    const healthStatus = this.getHealthStatus()
    console.log(`🏥 System Health: ${healthStatus.status} ${healthStatus.emoji}`)
    if (healthStatus.issues.length > 0) {
      healthStatus.issues.forEach((issue) => {
        console.log(`   ⚠️  ${issue}`)
      })
    }
    console.log()

    console.log('Press Ctrl+C to stop monitoring')
  }

  private getHealthStatus(): { status: string; emoji: string; issues: string[] } {
    const issues: string[] = []

    // Check connection health
    if (this.stats.connections.active === 0) {
      issues.push('No active connections')
    }

    if (this.stats.connections.failed > 10) {
      issues.push('High number of failed connections')
    }

    // Check message flow
    const runtime = (Date.now() - this.startTime) / 1000
    if (runtime > 60 && this.stats.messages.received === 0) {
      issues.push('No messages received in over 1 minute')
    }

    // Check latency
    if (this.stats.performance.avgLatency > 1000) {
      issues.push('High average latency (>1s)')
    }

    // Check error rate
    if (this.stats.errors.length > 5) {
      issues.push('High error rate')
    }

    if (issues.length === 0) {
      return { status: 'Healthy', emoji: '✅', issues: [] }
    } else if (issues.length <= 2) {
      return { status: 'Warning', emoji: '⚠️', issues }
    } else {
      return { status: 'Critical', emoji: '❌', issues }
    }
  }

  async stop(): Promise<void> {
    console.log('\n🛑 Stopping MQTT Monitor...')
    this.isRunning = false

    if (this.client) {
      this.client.end()
    }

    console.log('✅ MQTT Monitor stopped')
  }

  getStats(): MonitoringStats {
    return { ...this.stats }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...')
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
  process.exit(0)
})

// Run monitor if this file is executed directly
if (require.main === module) {
  const brokerUrl = process.argv[2] || 'mqtt://localhost:1883'
  const monitor = new MQTTMonitor(brokerUrl)

  monitor.start().catch((error) => {
    console.error('❌ Monitor failed to start:', error)
    process.exit(1)
  })
}

export { MQTTMonitor }
