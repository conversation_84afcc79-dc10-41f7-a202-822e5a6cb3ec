#!/usr/bin/env tsx

/**
 * Comprehensive MQTT System Testing Script
 * Tests all aspects of the MQTT migration including:
 * - Broker connectivity
 * - Authentication
 * - Message publishing/subscribing
 * - Topic permissions
 * - Performance under load
 */

import mqtt, { MqttClient } from 'mqtt'
import {
  generateFlutterCredentials,
  generateCoreCredentials,
  generateRealtimeCredentials
} from '../mqtt-auth'
import { performance } from 'perf_hooks'

interface TestResult {
  name: string
  success: boolean
  duration: number
  error?: string
  details?: any
}

class MQTTSystemTester {
  private brokerUrl: string
  private results: TestResult[] = []

  constructor(brokerUrl: string = 'mqtt://localhost:1883') {
    this.brokerUrl = brokerUrl
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting MQTT System Tests...\n')

    // Basic connectivity tests
    await this.testBrokerConnectivity()
    await this.testAnonymousConnection()

    // Authentication tests
    await this.testFlutterAuthentication()
    await this.testCoreAuthentication()
    await this.testRealtimeAuthentication()
    await this.testInvalidAuthentication()

    // Message flow tests
    await this.testBasicPubSub()
    await this.testRetainedMessages()
    await this.testQoSLevels()
    await this.testTopicPermissions()

    // Performance tests
    await this.testMessageThroughput()
    await this.testConcurrentConnections()

    // Reliability tests
    await this.testConnectionRecovery()
    await this.testMessagePersistence()

    this.printResults()
  }

  private async runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
    const startTime = performance.now()

    try {
      console.log(`🔄 Running: ${name}`)
      const result = await testFn()
      const duration = performance.now() - startTime

      const testResult: TestResult = {
        name,
        success: true,
        duration,
        details: result
      }

      console.log(`✅ ${name} - ${duration.toFixed(2)}ms`)
      this.results.push(testResult)
      return testResult
    } catch (error) {
      const duration = performance.now() - startTime

      const testResult: TestResult = {
        name,
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      }

      console.log(`❌ ${name} - ${error}`)
      this.results.push(testResult)
      return testResult
    }
  }

  private async testBrokerConnectivity(): Promise<void> {
    await this.runTest('Broker Connectivity', async () => {
      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          connectTimeout: 5000,
          clientId: `test_connectivity_${Date.now()}`
        })

        const timeout = setTimeout(() => {
          client.end()
          reject(new Error('Connection timeout'))
        }, 10000)

        client.on('connect', () => {
          clearTimeout(timeout)
          client.end()
          resolve({ connected: true })
        })

        client.on('error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })
    })
  }

  private async testAnonymousConnection(): Promise<void> {
    await this.runTest('Anonymous Connection (Should Fail)', async () => {
      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          connectTimeout: 5000,
          clientId: `test_anonymous_${Date.now()}`
          // No username/password
        })

        const timeout = setTimeout(() => {
          client.end()
          resolve({ rejected: true }) // This is expected
        }, 5000)

        client.on('connect', () => {
          clearTimeout(timeout)
          client.end()
          reject(new Error('Anonymous connection should have been rejected'))
        })

        client.on('error', () => {
          clearTimeout(timeout)
          resolve({ rejected: true }) // This is expected
        })
      })
    })
  }

  private async testFlutterAuthentication(): Promise<void> {
    await this.runTest('Flutter Client Authentication', async () => {
      const credentials = generateFlutterCredentials('test_user_123', 'test_device_456')

      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          connectTimeout: 5000,
          clientId: `test_flutter_${Date.now()}`,
          username: credentials.username,
          password: credentials.password
        })

        const timeout = setTimeout(() => {
          client.end()
          reject(new Error('Authentication timeout'))
        }, 10000)

        client.on('connect', () => {
          clearTimeout(timeout)
          client.end()
          resolve({ authenticated: true, credentials })
        })

        client.on('error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })
    })
  }

  private async testCoreAuthentication(): Promise<void> {
    await this.runTest('AdonisJS Client Authentication', async () => {
      const credentials = generateCoreCredentials('core_server')

      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          connectTimeout: 5000,
          clientId: `test_core_${Date.now()}`,
          username: credentials.username,
          password: credentials.password
        })

        const timeout = setTimeout(() => {
          client.end()
          reject(new Error('Authentication timeout'))
        }, 10000)

        client.on('connect', () => {
          clearTimeout(timeout)
          client.end()
          resolve({ authenticated: true, credentials })
        })

        client.on('error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })
    })
  }

  private async testRealtimeAuthentication(): Promise<void> {
    await this.runTest('Realtime Service Authentication', async () => {
      const credentials = generateRealtimeCredentials('realtime_server')

      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          connectTimeout: 5000,
          clientId: `test_realtime_${Date.now()}`,
          username: credentials.username,
          password: credentials.password
        })

        const timeout = setTimeout(() => {
          client.end()
          reject(new Error('Authentication timeout'))
        }, 10000)

        client.on('connect', () => {
          clearTimeout(timeout)
          client.end()
          resolve({ authenticated: true, credentials })
        })

        client.on('error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })
    })
  }

  private async testInvalidAuthentication(): Promise<void> {
    await this.runTest('Invalid Authentication (Should Fail)', async () => {
      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          connectTimeout: 5000,
          clientId: `test_invalid_${Date.now()}`,
          username: 'invalid_user',
          password: 'invalid_password'
        })

        const timeout = setTimeout(() => {
          client.end()
          resolve({ rejected: true }) // This is expected
        }, 5000)

        client.on('connect', () => {
          clearTimeout(timeout)
          client.end()
          reject(new Error('Invalid authentication should have been rejected'))
        })

        client.on('error', () => {
          clearTimeout(timeout)
          resolve({ rejected: true }) // This is expected
        })
      })
    })
  }

  private async testBasicPubSub(): Promise<void> {
    await this.runTest('Basic Publish/Subscribe', async () => {
      const credentials = generateRealtimeCredentials('test_pubsub')
      const testMessage = { test: true, timestamp: Date.now() }
      const testTopic = 'gomama/test/pubsub'

      return new Promise((resolve, reject) => {
        const publisher = mqtt.connect(this.brokerUrl, {
          clientId: `test_pub_${Date.now()}`,
          username: credentials.username,
          password: credentials.password
        })

        const subscriber = mqtt.connect(this.brokerUrl, {
          clientId: `test_sub_${Date.now()}`,
          username: credentials.username,
          password: credentials.password
        })

        let publisherConnected = false
        let subscriberConnected = false
        let messageReceived = false

        const cleanup = () => {
          publisher.end()
          subscriber.end()
        }

        const timeout = setTimeout(() => {
          cleanup()
          reject(new Error('Test timeout'))
        }, 15000)

        publisher.on('connect', () => {
          publisherConnected = true
          checkReady()
        })

        subscriber.on('connect', () => {
          subscriberConnected = true
          subscriber.subscribe(testTopic, { qos: 1 })
          checkReady()
        })

        subscriber.on('message', (topic, payload) => {
          if (topic === testTopic) {
            const received = JSON.parse(payload.toString())
            if (received.test === testMessage.test) {
              messageReceived = true
              clearTimeout(timeout)
              cleanup()
              resolve({
                published: true,
                received: true,
                message: received
              })
            }
          }
        })

        const checkReady = () => {
          if (publisherConnected && subscriberConnected && !messageReceived) {
            setTimeout(() => {
              publisher.publish(testTopic, JSON.stringify(testMessage), { qos: 1 })
            }, 100)
          }
        }

        publisher.on('error', reject)
        subscriber.on('error', reject)
      })
    })
  }

  private async testRetainedMessages(): Promise<void> {
    await this.runTest('Retained Messages', async () => {
      const credentials = generateRealtimeCredentials('test_retained')
      const testMessage = { retained: true, timestamp: Date.now() }
      const testTopic = 'gomama/test/retained'

      return new Promise((resolve, reject) => {
        const publisher = mqtt.connect(this.brokerUrl, {
          clientId: `test_retained_pub_${Date.now()}`,
          username: credentials.username,
          password: credentials.password
        })

        publisher.on('connect', () => {
          // Publish retained message
          publisher.publish(
            testTopic,
            JSON.stringify(testMessage),
            {
              qos: 1,
              retain: true
            },
            () => {
              publisher.end()

              // Connect new subscriber to check if retained message is received
              setTimeout(() => {
                const subscriber = mqtt.connect(this.brokerUrl, {
                  clientId: `test_retained_sub_${Date.now()}`,
                  username: credentials.username,
                  password: credentials.password
                })

                const timeout = setTimeout(() => {
                  subscriber.end()
                  reject(new Error('Retained message not received'))
                }, 10000)

                subscriber.on('connect', () => {
                  subscriber.subscribe(testTopic, { qos: 1 })
                })

                subscriber.on('message', (topic, payload) => {
                  if (topic === testTopic) {
                    const received = JSON.parse(payload.toString())
                    if (received.retained === testMessage.retained) {
                      clearTimeout(timeout)
                      subscriber.end()
                      resolve({
                        published: true,
                        retained: true,
                        received
                      })
                    }
                  }
                })

                subscriber.on('error', reject)
              }, 1000)
            }
          )
        })

        publisher.on('error', reject)
      })
    })
  }

  private async testQoSLevels(): Promise<void> {
    await this.runTest('QoS Levels', async () => {
      const credentials = generateRealtimeCredentials('test_qos')
      const results = []

      for (const qos of [0, 1, 2] as const) {
        const testMessage = { qos, timestamp: Date.now() }
        const testTopic = `gomama/test/qos${qos}`

        const result = await new Promise((resolve, reject) => {
          const client = mqtt.connect(this.brokerUrl, {
            clientId: `test_qos${qos}_${Date.now()}`,
            username: credentials.username,
            password: credentials.password
          })

          const timeout = setTimeout(() => {
            client.end()
            reject(new Error(`QoS ${qos} test timeout`))
          }, 10000)

          client.on('connect', () => {
            client.subscribe(testTopic, { qos })

            setTimeout(() => {
              client.publish(testTopic, JSON.stringify(testMessage), { qos })
            }, 100)
          })

          client.on('message', (topic, payload) => {
            if (topic === testTopic) {
              const received = JSON.parse(payload.toString())
              if (received.qos === qos) {
                clearTimeout(timeout)
                client.end()
                resolve({ qos, success: true })
              }
            }
          })

          client.on('error', reject)
        })

        results.push(result)
      }

      return { qosTests: results }
    })
  }

  private async testMessageThroughput(): Promise<void> {
    await this.runTest('Message Throughput', async () => {
      const credentials = generateRealtimeCredentials('test_throughput')
      const messageCount = 100
      const testTopic = 'gomama/test/throughput'

      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          clientId: `test_throughput_${Date.now()}`,
          username: credentials.username,
          password: credentials.password
        })

        let receivedCount = 0
        const startTime = performance.now()

        const timeout = setTimeout(() => {
          client.end()
          reject(new Error('Throughput test timeout'))
        }, 30000)

        client.on('connect', () => {
          client.subscribe(testTopic, { qos: 1 })

          // Publish messages rapidly
          for (let i = 0; i < messageCount; i++) {
            client.publish(
              testTopic,
              JSON.stringify({
                id: i,
                timestamp: Date.now()
              }),
              { qos: 1 }
            )
          }
        })

        client.on('message', () => {
          receivedCount++

          if (receivedCount === messageCount) {
            const endTime = performance.now()
            const duration = endTime - startTime
            const messagesPerSecond = (messageCount / duration) * 1000

            clearTimeout(timeout)
            client.end()
            resolve({
              messageCount,
              duration,
              messagesPerSecond: Math.round(messagesPerSecond)
            })
          }
        })

        client.on('error', reject)
      })
    })
  }

  private async testConcurrentConnections(): Promise<void> {
    await this.runTest('Concurrent Connections', async () => {
      const connectionCount = 10
      const credentials = generateRealtimeCredentials('test_concurrent')
      const connections: MqttClient[] = []

      return new Promise((resolve, reject) => {
        let connectedCount = 0
        const timeout = setTimeout(() => {
          connections.forEach((client) => client.end())
          reject(new Error('Concurrent connections test timeout'))
        }, 20000)

        for (let i = 0; i < connectionCount; i++) {
          const client = mqtt.connect(this.brokerUrl, {
            clientId: `test_concurrent_${i}_${Date.now()}`,
            username: credentials.username,
            password: credentials.password
          })

          connections.push(client)

          client.on('connect', () => {
            connectedCount++

            if (connectedCount === connectionCount) {
              clearTimeout(timeout)

              // Disconnect all clients
              connections.forEach((c) => c.end())

              resolve({
                targetConnections: connectionCount,
                successfulConnections: connectedCount
              })
            }
          })

          client.on('error', (error) => {
            clearTimeout(timeout)
            connections.forEach((c) => c.end())
            reject(error)
          })
        }
      })
    })
  }

  private async testConnectionRecovery(): Promise<void> {
    await this.runTest('Connection Recovery', async () => {
      // This test would require broker restart simulation
      // For now, just test reconnection logic
      const credentials = generateRealtimeCredentials('test_recovery')

      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          clientId: `test_recovery_${Date.now()}`,
          username: credentials.username,
          password: credentials.password,
          reconnectPeriod: 1000
        })

        let connected = false
        let reconnected = false

        const timeout = setTimeout(() => {
          client.end()
          reject(new Error('Connection recovery test timeout'))
        }, 15000)

        client.on('connect', () => {
          if (!connected) {
            connected = true
            // Simulate disconnection
            setTimeout(() => {
              client.stream.destroy()
            }, 1000)
          } else {
            reconnected = true
            clearTimeout(timeout)
            client.end()
            resolve({
              initialConnection: true,
              reconnection: true
            })
          }
        })

        client.on('error', (error) => {
          // Ignore connection errors during test
          if (!connected) {
            clearTimeout(timeout)
            reject(error)
          }
        })
      })
    })
  }

  private async testMessagePersistence(): Promise<void> {
    await this.runTest('Message Persistence', async () => {
      // Test that messages are persisted for offline clients
      const credentials = generateRealtimeCredentials('test_persistence')
      const testTopic = 'gomama/test/persistence'
      const testMessage = { persistent: true, timestamp: Date.now() }

      return new Promise((resolve, reject) => {
        // First, connect and subscribe with clean session = false
        const subscriber = mqtt.connect(this.brokerUrl, {
          clientId: 'test_persistence_subscriber',
          username: credentials.username,
          password: credentials.password,
          clean: false
        })

        subscriber.on('connect', () => {
          subscriber.subscribe(testTopic, { qos: 1 }, () => {
            // Disconnect subscriber
            subscriber.end(false) // Don't clean session

            // Publish message while subscriber is offline
            setTimeout(() => {
              const publisher = mqtt.connect(this.brokerUrl, {
                clientId: `test_persistence_pub_${Date.now()}`,
                username: credentials.username,
                password: credentials.password
              })

              publisher.on('connect', () => {
                publisher.publish(testTopic, JSON.stringify(testMessage), { qos: 1 }, () => {
                  publisher.end()

                  // Reconnect subscriber to check for persisted message
                  setTimeout(() => {
                    const reconnectedSubscriber = mqtt.connect(this.brokerUrl, {
                      clientId: 'test_persistence_subscriber',
                      username: credentials.username,
                      password: credentials.password,
                      clean: false
                    })

                    const timeout = setTimeout(() => {
                      reconnectedSubscriber.end()
                      reject(new Error('Persisted message not received'))
                    }, 10000)

                    reconnectedSubscriber.on('message', (topic, payload) => {
                      if (topic === testTopic) {
                        const received = JSON.parse(payload.toString())
                        if (received.persistent === testMessage.persistent) {
                          clearTimeout(timeout)
                          reconnectedSubscriber.end()
                          resolve({
                            published: true,
                            persisted: true,
                            received
                          })
                        }
                      }
                    })

                    reconnectedSubscriber.on('error', reject)
                  }, 1000)
                })
              })

              publisher.on('error', reject)
            }, 1000)
          })
        })

        subscriber.on('error', reject)
      })
    })
  }

  private async testTopicPermissions(): Promise<void> {
    await this.runTest('Topic Permissions', async () => {
      // This would test ACL rules - simplified for now
      const flutterCredentials = generateFlutterCredentials('test_user_123', 'test_device_456')

      return new Promise((resolve, reject) => {
        const client = mqtt.connect(this.brokerUrl, {
          clientId: `test_permissions_${Date.now()}`,
          username: flutterCredentials.username,
          password: flutterCredentials.password
        })

        const timeout = setTimeout(() => {
          client.end()
          reject(new Error('Permission test timeout'))
        }, 10000)

        client.on('connect', () => {
          // Test subscribing to allowed topic
          client.subscribe('gomama/users/notifications/test_user_123', { qos: 1 }, (err) => {
            if (err) {
              clearTimeout(timeout)
              client.end()
              reject(new Error('Failed to subscribe to allowed topic'))
            } else {
              clearTimeout(timeout)
              client.end()
              resolve({
                allowedTopicSubscription: true
              })
            }
          })
        })

        client.on('error', reject)
      })
    })
  }

  private printResults(): void {
    console.log('\n' + '='.repeat(60))
    console.log('📊 MQTT System Test Results')
    console.log('='.repeat(60))

    const passed = this.results.filter((r) => r.success).length
    const failed = this.results.filter((r) => !r.success).length
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)

    console.log(`\n📈 Summary:`)
    console.log(`   Total Tests: ${this.results.length}`)
    console.log(`   Passed: ${passed} ✅`)
    console.log(`   Failed: ${failed} ❌`)
    console.log(`   Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`)
    console.log(`   Total Duration: ${totalDuration.toFixed(2)}ms`)

    if (failed > 0) {
      console.log(`\n❌ Failed Tests:`)
      this.results
        .filter((r) => !r.success)
        .forEach((r) => {
          console.log(`   • ${r.name}: ${r.error}`)
        })
    }

    console.log(`\n✅ Passed Tests:`)
    this.results
      .filter((r) => r.success)
      .forEach((r) => {
        console.log(`   • ${r.name} (${r.duration.toFixed(2)}ms)`)
      })

    console.log('\n' + '='.repeat(60))

    if (failed === 0) {
      console.log('🎉 All tests passed! MQTT system is ready for production.')
    } else {
      console.log('⚠️  Some tests failed. Please review and fix issues before deployment.')
      process.exit(1)
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const brokerUrl = process.argv[2] || 'mqtt://localhost:1883'
  const tester = new MQTTSystemTester(brokerUrl)

  tester.runAllTests().catch((error) => {
    console.error('❌ Test execution failed:', error)
    process.exit(1)
  })
}

export { MQTTSystemTester }

// Additional utility functions for testing
export async function quickHealthCheck(
  brokerUrl: string = 'mqtt://localhost:1883'
): Promise<boolean> {
  const credentials = generateRealtimeCredentials('health_check')

  return new Promise((resolve) => {
    const client = mqtt.connect(brokerUrl, {
      clientId: `health_check_${Date.now()}`,
      username: credentials.username,
      password: credentials.password,
      connectTimeout: 5000
    })

    const timeout = setTimeout(() => {
      client.end()
      resolve(false)
    }, 10000)

    client.on('connect', () => {
      clearTimeout(timeout)
      client.end()
      resolve(true)
    })

    client.on('error', () => {
      clearTimeout(timeout)
      resolve(false)
    })
  })
}
