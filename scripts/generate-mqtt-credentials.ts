#!/usr/bin/env tsx

/**
 * <PERSON>ript to generate MQTT credentials for different client types
 * Usage: tsx scripts/generate-mqtt-credentials.ts [client-type] [user-id] [device-id]
 */

import {
  generateFlutterCredentials,
  generateCoreCredentials,
  generateRealtimeCredentials,
  getMQTTAuthService
} from '../mqtt-auth'

function printUsage() {
  console.log(`
Usage: tsx scripts/generate-mqtt-credentials.ts [client-type] [user-id] [device-id]

Client Types:
  flutter    - Generate credentials for Flutter mobile app
  core       - Generate credentials for Core backend
  realtime   - Generate credentials for realtime service
  admin      - Generate admin credentials

Examples:
  tsx scripts/generate-mqtt-credentials.ts flutter user123 device456
  tsx scripts/generate-mqtt-credentials.ts core server
  tsx scripts/generate-mqtt-credentials.ts realtime
  tsx scripts/generate-mqtt-credentials.ts admin admin_user
`)
}

function generateCredentials() {
  const args = process.argv.slice(2)

  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    printUsage()
    return
  }

  const clientType = args[0]
  const userId = args[1] || 'default_user'
  const deviceId = args[2]

  let credentials: { username: string; password: string; token: string }

  switch (clientType) {
    case 'flutter':
      if (!deviceId) {
        console.error('❌ Device ID is required for Flutter clients')
        console.log('Usage: tsx scripts/generate-mqtt-credentials.ts flutter [user-id] [device-id]')
        process.exit(1)
      }
      credentials = generateFlutterCredentials(userId, deviceId)
      break

    case 'core':
      credentials = generateCoreCredentials(userId)
      break

    case 'realtime':
      credentials = generateRealtimeCredentials(userId)
      break

    case 'admin':
      const authService = getMQTTAuthService()
      credentials = authService.generateClientCredentials(userId, 'admin')
      break

    default:
      console.error(`❌ Unknown client type: ${clientType}`)
      printUsage()
      process.exit(1)
  }

  console.log(`
🔐 MQTT Credentials Generated for ${clientType.toUpperCase()}

Client Details:
  User ID: ${userId}
  ${deviceId ? `Device ID: ${deviceId}` : ''}
  Client Type: ${clientType}

MQTT Connection Details:
  Username: ${credentials.username}
  Password: ${credentials.password}
  
JWT Token:
  ${credentials.token}

Connection Example (MQTT CLI):
  mqtt sub -h localhost -p 1883 -u "${credentials.username}" -P "${
    credentials.password
  }" -t "gomama/+/+/+"

Connection Example (Node.js):
  const client = mqtt.connect('mqtt://localhost:1883', {
    username: '${credentials.username}',
    password: '${credentials.password}',
    clientId: '${clientType}_${userId}_${Date.now()}'
  })

Environment Variables (for .env):
  MQTT_USERNAME=${credentials.username}
  MQTT_PASSWORD=${credentials.password}
`)

  // Save to file if requested
  if (args.includes('--save')) {
    const fs = require('fs')
    const filename = `mqtt-credentials-${clientType}-${userId}.json`
    const data = {
      clientType,
      userId,
      deviceId,
      credentials,
      generatedAt: new Date().toISOString()
    }

    fs.writeFileSync(filename, JSON.stringify(data, null, 2))
    console.log(`💾 Credentials saved to: ${filename}`)
  }
}

// Generate test credentials for all client types
function generateTestCredentials() {
  console.log('🧪 Generating test credentials for all client types...\n')

  const testCases = [
    { type: 'flutter', userId: 'test_user_123', deviceId: 'test_device_456' },
    { type: 'core', userId: 'core_server' },
    { type: 'realtime', userId: 'realtime_server' },
    { type: 'admin', userId: 'admin_user' }
  ]

  testCases.forEach(({ type, userId, deviceId }) => {
    console.log(`\n--- ${type.toUpperCase()} CREDENTIALS ---`)

    let credentials: { username: string; password: string; token: string }

    switch (type) {
      case 'flutter':
        credentials = generateFlutterCredentials(userId, deviceId!)
        break
      case 'core':
        credentials = generateCoreCredentials(userId)
        break
      case 'realtime':
        credentials = generateRealtimeCredentials(userId)
        break
      case 'admin':
        const authService = getMQTTAuthService()
        credentials = authService.generateClientCredentials(userId, 'admin')
        break
      default:
        return
    }

    console.log(`Username: ${credentials.username}`)
    console.log(`Password: ${credentials.password.substring(0, 50)}...`)
    console.log(`Token: ${credentials.token.substring(0, 50)}...`)
  })

  console.log('\n✅ All test credentials generated!')
}

// Main execution
if (require.main === module) {
  if (process.argv.includes('--test')) {
    generateTestCredentials()
  } else {
    generateCredentials()
  }
}
