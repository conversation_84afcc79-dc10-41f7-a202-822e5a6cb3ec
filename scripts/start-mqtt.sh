#!/bin/bash

# GoMama MQTT Migration Startup Script
# This script helps start the MQTT broker and realtime service

set -e

echo "🚀 Starting GoMama MQTT Migration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please update the .env file with your actual configuration values"
    else
        print_error ".env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p emqx/data emqx/log redis/data
print_success "Directories created"

# Stop any existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose.mqtt.yml down --remove-orphans 2>/dev/null || true

# Start MQTT broker and Redis first
print_status "Starting MQTT broker (EMQX) and Redis..."
docker-compose -f docker-compose.mqtt.yml up -d emqx redis

# Wait for EMQX to be ready
print_status "Waiting for EMQX to be ready..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose.mqtt.yml exec -T emqx /opt/emqx/bin/emqx ctl status > /dev/null 2>&1; then
        print_success "EMQX is ready!"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    echo -n "."
done

if [ $counter -ge $timeout ]; then
    print_error "EMQX failed to start within $timeout seconds"
    exit 1
fi

# Wait for Redis to be ready
print_status "Waiting for Redis to be ready..."
timeout=30
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose -f docker-compose.mqtt.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is ready!"
        break
    fi
    sleep 1
    counter=$((counter + 1))
    echo -n "."
done

if [ $counter -ge $timeout ]; then
    print_error "Redis failed to start within $timeout seconds"
    exit 1
fi

# Install npm dependencies if needed
if [ ! -d node_modules ]; then
    print_status "Installing npm dependencies..."
    npm install
    print_success "Dependencies installed"
fi

# Build TypeScript if needed
print_status "Building TypeScript..."
npm run build
print_success "TypeScript built"

# Start the realtime service
print_status "Starting GoMama Realtime service..."
if [ "$1" = "--docker" ]; then
    # Start with Docker
    docker-compose -f docker-compose.mqtt.yml up -d gomama_realtime
    print_success "GoMama Realtime service started with Docker"
else
    # Start with npm (development mode)
    print_success "Starting in development mode..."
    print_status "Use 'npm run dev' to start the service"
fi

# Display connection information
echo ""
print_success "🎉 MQTT Migration Setup Complete!"
echo ""
echo "📊 Service URLs:"
echo "   • EMQX Dashboard: http://localhost:18083 (admin/gomama2024!)"
echo "   • MQTT Broker: mqtt://localhost:1883"
echo "   • MQTT WebSocket: ws://localhost:8083/mqtt"
# echo "   • Realtime Metrics: http://localhost:9001/metrics"
echo "   • Realtime Metrics: http://localhost:3334/metrics"
echo ""
echo "🔧 Next Steps:"
echo "   1. Update your .env file with actual configuration values"
echo "   2. Configure EMQX authentication in the dashboard"
echo "   3. Test MQTT connection with a client"
echo "   4. Update AdonisJS to publish to MQTT topics"
echo "   5. Update Flutter app to use MQTT instead of WebSocket"
echo ""

# Show logs if requested
if [ "$1" = "--logs" ] || [ "$2" = "--logs" ]; then
    print_status "Showing logs (Ctrl+C to exit)..."
    docker-compose -f docker-compose.mqtt.yml logs -f
fi
