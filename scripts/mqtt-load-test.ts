#!/usr/bin/env tsx

/**
 * MQTT Load Testing Script
 * Simulates high load scenarios to test MQTT broker performance
 */

import mqtt, { MqttClient } from 'mqtt'
import { generateFlutterCredentials, generateRealtimeCredentials } from '../mqtt-auth'
import { performance } from 'perf_hooks'

interface LoadTestConfig {
  brokerUrl: string
  concurrentClients: number
  messagesPerClient: number
  messageInterval: number // milliseconds
  testDuration: number // seconds
  qos: 0 | 1 | 2
  payloadSize: number // bytes
  retainMessages: boolean
}

interface LoadTestResults {
  config: LoadTestConfig
  startTime: number
  endTime: number
  duration: number
  totalMessages: {
    sent: number
    received: number
    failed: number
  }
  performance: {
    messagesPerSecond: number
    avgLatency: number
    maxLatency: number
    minLatency: number
    p95Latency: number
    p99Latency: number
  }
  connections: {
    successful: number
    failed: number
    disconnected: number
  }
  errors: string[]
}

class MQTTLoadTester {
  private clients: MqttClient[] = []
  private results: LoadTestResults
  private latencies: number[] = []
  private isRunning = false

  constructor(private config: LoadTestConfig) {
    this.results = this.initializeResults()
  }

  private initializeResults(): LoadTestResults {
    return {
      config: this.config,
      startTime: 0,
      endTime: 0,
      duration: 0,
      totalMessages: {
        sent: 0,
        received: 0,
        failed: 0
      },
      performance: {
        messagesPerSecond: 0,
        avgLatency: 0,
        maxLatency: 0,
        minLatency: Infinity,
        p95Latency: 0,
        p99Latency: 0
      },
      connections: {
        successful: 0,
        failed: 0,
        disconnected: 0
      },
      errors: []
    }
  }

  async runLoadTest(): Promise<LoadTestResults> {
    console.log('🚀 Starting MQTT Load Test...')
    console.log(`📊 Configuration:`)
    console.log(`   Broker: ${this.config.brokerUrl}`)
    console.log(`   Concurrent Clients: ${this.config.concurrentClients}`)
    console.log(`   Messages per Client: ${this.config.messagesPerClient}`)
    console.log(`   Message Interval: ${this.config.messageInterval}ms`)
    console.log(`   Test Duration: ${this.config.testDuration}s`)
    console.log(`   QoS Level: ${this.config.qos}`)
    console.log(`   Payload Size: ${this.config.payloadSize} bytes`)
    console.log(`   Retain Messages: ${this.config.retainMessages}`)
    console.log()

    this.results.startTime = performance.now()
    this.isRunning = true

    try {
      // Create and connect clients
      await this.createClients()

      // Start message publishing
      await this.startMessageFlow()

      // Wait for test duration
      await this.waitForCompletion()

      // Calculate final results
      this.calculateResults()

      console.log('✅ Load test completed successfully')
      return this.results
    } catch (error) {
      console.error('❌ Load test failed:', error)
      this.results.errors.push(`Test failed: ${error}`)
      throw error
    } finally {
      await this.cleanup()
    }
  }

  private async createClients(): Promise<void> {
    console.log('🔌 Creating and connecting clients...')

    const connectionPromises: Promise<void>[] = []

    for (let i = 0; i < this.config.concurrentClients; i++) {
      const promise = this.createClient(i)
      connectionPromises.push(promise)
    }

    // Wait for all connections with timeout
    const timeout = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout')), 30000)
    })

    try {
      await Promise.race([Promise.allSettled(connectionPromises), timeout])

      console.log(
        `✅ Connected ${this.results.connections.successful}/${this.config.concurrentClients} clients`
      )

      if (this.results.connections.successful === 0) {
        throw new Error('No clients connected successfully')
      }
    } catch (error) {
      throw new Error(`Failed to connect clients: ${error}`)
    }
  }

  private async createClient(clientIndex: number): Promise<void> {
    return new Promise((resolve, reject) => {
      // Alternate between Flutter and Realtime credentials for variety
      const credentials =
        clientIndex % 2 === 0
          ? generateFlutterCredentials(`load_test_user_${clientIndex}`, `device_${clientIndex}`)
          : generateRealtimeCredentials(`load_test_realtime_${clientIndex}`)

      const client = mqtt.connect(this.config.brokerUrl, {
        clientId: `load_test_${clientIndex}_${Date.now()}`,
        username: credentials.username,
        password: credentials.password,
        keepalive: 60,
        connectTimeout: 10000
      })

      const timeout = setTimeout(() => {
        client.end()
        this.results.connections.failed++
        reject(new Error(`Client ${clientIndex} connection timeout`))
      }, 15000)

      client.on('connect', () => {
        clearTimeout(timeout)
        this.results.connections.successful++
        this.clients.push(client)

        // Subscribe to test topics
        const subscribeTopic = `gomama/load-test/client-${clientIndex}/+`
        client.subscribe(subscribeTopic, { qos: this.config.qos })

        resolve()
      })

      client.on('message', (topic, payload) => {
        this.handleMessage(topic, payload)
      })

      client.on('error', (error) => {
        clearTimeout(timeout)
        this.results.connections.failed++
        this.results.errors.push(`Client ${clientIndex} error: ${error.message}`)
        reject(error)
      })

      client.on('disconnect', () => {
        this.results.connections.disconnected++
      })
    })
  }

  private handleMessage(topic: string, payload: Buffer): void {
    this.results.totalMessages.received++

    try {
      const message = JSON.parse(payload.toString())
      if (message.timestamp) {
        const latency = performance.now() - message.timestamp
        this.latencies.push(latency)

        // Update latency stats
        this.results.performance.maxLatency = Math.max(this.results.performance.maxLatency, latency)
        this.results.performance.minLatency = Math.min(this.results.performance.minLatency, latency)
      }
    } catch {
      // Ignore non-JSON messages
    }
  }

  private async startMessageFlow(): Promise<void> {
    console.log('📨 Starting message flow...')

    const publishPromises: Promise<void>[] = []

    this.clients.forEach((client, clientIndex) => {
      const promise = this.publishMessages(client, clientIndex)
      publishPromises.push(promise)
    })

    // Don't wait for all messages to complete, just start them
    Promise.allSettled(publishPromises)
  }

  private async publishMessages(client: MqttClient, clientIndex: number): Promise<void> {
    const topic = `gomama/load-test/client-${clientIndex}/messages`
    const payload = this.generatePayload()

    let messageCount = 0

    const publishInterval = setInterval(() => {
      if (!this.isRunning || messageCount >= this.config.messagesPerClient) {
        clearInterval(publishInterval)
        return
      }

      const message = {
        clientIndex,
        messageId: messageCount,
        timestamp: performance.now(),
        payload
      }

      client.publish(
        topic,
        JSON.stringify(message),
        {
          qos: this.config.qos,
          retain: this.config.retainMessages
        },
        (error) => {
          if (error) {
            this.results.totalMessages.failed++
            this.results.errors.push(`Publish error: ${error.message}`)
          } else {
            this.results.totalMessages.sent++
          }
        }
      )

      messageCount++
    }, this.config.messageInterval)
  }

  private generatePayload(): string {
    // Generate payload of specified size
    const basePayload = {
      loadTest: true,
      timestamp: Date.now(),
      data: ''
    }

    const baseSize = JSON.stringify(basePayload).length
    const remainingSize = Math.max(0, this.config.payloadSize - baseSize)

    basePayload.data = 'x'.repeat(remainingSize)

    return JSON.stringify(basePayload)
  }

  private async waitForCompletion(): Promise<void> {
    console.log(`⏳ Running test for ${this.config.testDuration} seconds...`)

    const startTime = Date.now()
    const endTime = startTime + this.config.testDuration * 1000

    // Progress reporting
    const progressInterval = setInterval(() => {
      const elapsed = (Date.now() - startTime) / 1000
      const remaining = Math.max(0, this.config.testDuration - elapsed)
      const progress = (elapsed / this.config.testDuration) * 100

      process.stdout.write(
        `\r📊 Progress: ${progress.toFixed(1)}% | ` +
          `Sent: ${this.results.totalMessages.sent} | ` +
          `Received: ${this.results.totalMessages.received} | ` +
          `Remaining: ${remaining.toFixed(0)}s`
      )

      if (remaining <= 0) {
        clearInterval(progressInterval)
        console.log() // New line
      }
    }, 1000)

    // Wait for test duration
    await new Promise((resolve) => setTimeout(resolve, this.config.testDuration * 1000))

    this.isRunning = false
    clearInterval(progressInterval)

    // Wait a bit more for final messages
    console.log('⏳ Waiting for final messages...')
    await new Promise((resolve) => setTimeout(resolve, 2000))
  }

  private calculateResults(): void {
    this.results.endTime = performance.now()
    this.results.duration = this.results.endTime - this.results.startTime

    // Calculate performance metrics
    const durationSeconds = this.results.duration / 1000
    this.results.performance.messagesPerSecond = this.results.totalMessages.sent / durationSeconds

    if (this.latencies.length > 0) {
      // Sort latencies for percentile calculations
      this.latencies.sort((a, b) => a - b)

      this.results.performance.avgLatency =
        this.latencies.reduce((sum, l) => sum + l, 0) / this.latencies.length

      const p95Index = Math.floor(this.latencies.length * 0.95)
      const p99Index = Math.floor(this.latencies.length * 0.99)

      this.results.performance.p95Latency = this.latencies[p95Index] || 0
      this.results.performance.p99Latency = this.latencies[p99Index] || 0
    }
  }

  private async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up clients...')

    const disconnectPromises = this.clients.map((client) => {
      return new Promise<void>((resolve) => {
        client.end(false, {}, () => resolve())
      })
    })

    await Promise.allSettled(disconnectPromises)
    this.clients = []

    console.log('✅ Cleanup completed')
  }

  printResults(): void {
    console.log('\n' + '='.repeat(60))
    console.log('📊 MQTT Load Test Results')
    console.log('='.repeat(60))

    console.log(`\n⏱️  Test Duration: ${(this.results.duration / 1000).toFixed(2)}s`)

    console.log(`\n🔌 Connection Results:`)
    console.log(`   Successful: ${this.results.connections.successful}`)
    console.log(`   Failed: ${this.results.connections.failed}`)
    console.log(`   Disconnected: ${this.results.connections.disconnected}`)

    console.log(`\n📨 Message Results:`)
    console.log(`   Sent: ${this.results.totalMessages.sent.toLocaleString()}`)
    console.log(`   Received: ${this.results.totalMessages.received.toLocaleString()}`)
    console.log(`   Failed: ${this.results.totalMessages.failed.toLocaleString()}`)
    console.log(
      `   Success Rate: ${(
        ((this.results.totalMessages.sent - this.results.totalMessages.failed) /
          this.results.totalMessages.sent) *
        100
      ).toFixed(2)}%`
    )

    console.log(`\n⚡ Performance Results:`)
    console.log(`   Throughput: ${this.results.performance.messagesPerSecond.toFixed(2)} msg/sec`)

    if (this.latencies.length > 0) {
      console.log(`   Avg Latency: ${this.results.performance.avgLatency.toFixed(2)}ms`)
      console.log(`   Min Latency: ${this.results.performance.minLatency.toFixed(2)}ms`)
      console.log(`   Max Latency: ${this.results.performance.maxLatency.toFixed(2)}ms`)
      console.log(`   P95 Latency: ${this.results.performance.p95Latency.toFixed(2)}ms`)
      console.log(`   P99 Latency: ${this.results.performance.p99Latency.toFixed(2)}ms`)
    }

    if (this.results.errors.length > 0) {
      console.log(`\n❌ Errors (${this.results.errors.length}):`)
      this.results.errors.slice(0, 10).forEach((error) => {
        console.log(`   ${error}`)
      })
      if (this.results.errors.length > 10) {
        console.log(`   ... and ${this.results.errors.length - 10} more`)
      }
    }

    console.log('\n' + '='.repeat(60))
  }
}

// Predefined test configurations
const TEST_CONFIGS: { [key: string]: Partial<LoadTestConfig> } = {
  light: {
    concurrentClients: 10,
    messagesPerClient: 100,
    messageInterval: 1000,
    testDuration: 60,
    qos: 1,
    payloadSize: 256,
    retainMessages: false
  } as Partial<LoadTestConfig>,

  medium: {
    concurrentClients: 50,
    messagesPerClient: 200,
    messageInterval: 500,
    testDuration: 120,
    qos: 1,
    payloadSize: 512,
    retainMessages: false
  } as Partial<LoadTestConfig>,

  heavy: {
    concurrentClients: 100,
    messagesPerClient: 500,
    messageInterval: 100,
    testDuration: 300,
    qos: 1,
    payloadSize: 1024,
    retainMessages: false
  } as Partial<LoadTestConfig>
}

// Run load test if this file is executed directly
if (require.main === module) {
  const brokerUrl = process.argv[2] || 'mqtt://localhost:1883'
  const testType = process.argv[3] || 'light'

  if (!TEST_CONFIGS[testType]) {
    console.error(`❌ Unknown test type: ${testType}`)
    console.log('Available test types: light, medium, heavy')
    process.exit(1)
  }

  const config: LoadTestConfig = {
    brokerUrl,
    ...TEST_CONFIGS[testType]
  } as LoadTestConfig

  const tester = new MQTTLoadTester(config)

  tester
    .runLoadTest()
    .then((results) => {
      tester.printResults()

      // Exit with error code if test had significant failures
      const failureRate = results.totalMessages.failed / results.totalMessages.sent
      if (failureRate > 0.05) {
        // More than 5% failure rate
        console.log('\n❌ Test failed due to high failure rate')
        process.exit(1)
      }
    })
    .catch((error) => {
      console.error('❌ Load test execution failed:', error)
      process.exit(1)
    })
}

export { MQTTLoadTester, LoadTestConfig, LoadTestResults }
