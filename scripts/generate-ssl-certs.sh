#!/bin/bash

# Generate SSL certificates for MQTT broker
# This script creates self-signed certificates for development/testing

set -e

CERT_DIR="emqx/etc/certs"
DAYS=365
COUNTRY="US"
STATE="CA"
CITY="San Francisco"
ORG="GoMama"
ORG_UNIT="Development"
COMMON_NAME="localhost"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🔐 Generating SSL certificates for MQTT broker..."

# Create certificate directory
mkdir -p "$CERT_DIR"

# Generate CA private key
print_status "Generating CA private key..."
openssl genrsa -out "$CERT_DIR/ca-key.pem" 4096

# Generate CA certificate
print_status "Generating CA certificate..."
openssl req -new -x509 -days $DAYS -key "$CERT_DIR/ca-key.pem" -out "$CERT_DIR/cacert.pem" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$ORG_UNIT CA/CN=GoMama CA"

# Generate server private key
print_status "Generating server private key..."
openssl genrsa -out "$CERT_DIR/key.pem" 4096

# Generate server certificate signing request
print_status "Generating server certificate signing request..."
openssl req -new -key "$CERT_DIR/key.pem" -out "$CERT_DIR/server.csr" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$ORG_UNIT/CN=$COMMON_NAME"

# Create extensions file for server certificate
cat > "$CERT_DIR/server.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = emqx
DNS.3 = gomama_emqx
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Generate server certificate signed by CA
print_status "Generating server certificate..."
openssl x509 -req -in "$CERT_DIR/server.csr" -CA "$CERT_DIR/cacert.pem" -CAkey "$CERT_DIR/ca-key.pem" -CAcreateserial -out "$CERT_DIR/cert.pem" -days $DAYS -extensions v3_req -extfile "$CERT_DIR/server.ext"

# Generate client private key (for client certificate authentication if needed)
print_status "Generating client private key..."
openssl genrsa -out "$CERT_DIR/client-key.pem" 4096

# Generate client certificate signing request
print_status "Generating client certificate signing request..."
openssl req -new -key "$CERT_DIR/client-key.pem" -out "$CERT_DIR/client.csr" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$ORG_UNIT/CN=GoMama Client"

# Generate client certificate signed by CA
print_status "Generating client certificate..."
openssl x509 -req -in "$CERT_DIR/client.csr" -CA "$CERT_DIR/cacert.pem" -CAkey "$CERT_DIR/ca-key.pem" -CAcreateserial -out "$CERT_DIR/client-cert.pem" -days $DAYS

# Set appropriate permissions
chmod 600 "$CERT_DIR"/*.pem
chmod 644 "$CERT_DIR/cacert.pem" "$CERT_DIR/cert.pem" "$CERT_DIR/client-cert.pem"

# Clean up temporary files
rm -f "$CERT_DIR/server.csr" "$CERT_DIR/client.csr" "$CERT_DIR/server.ext" "$CERT_DIR/cacert.srl"

print_success "✅ SSL certificates generated successfully!"

echo ""
echo "📁 Certificate files created:"
echo "   • CA Certificate: $CERT_DIR/cacert.pem"
echo "   • Server Certificate: $CERT_DIR/cert.pem"
echo "   • Server Private Key: $CERT_DIR/key.pem"
echo "   • Client Certificate: $CERT_DIR/client-cert.pem"
echo "   • Client Private Key: $CERT_DIR/client-key.pem"
echo ""

print_warning "⚠️  These are self-signed certificates for development only!"
print_warning "⚠️  For production, use certificates from a trusted CA."

echo ""
echo "🔧 MQTT SSL Connection Examples:"
echo ""
echo "MQTT over SSL (port 8883):"
echo "  mqtt sub -h localhost -p 8883 --cafile $CERT_DIR/cacert.pem -t 'test/topic'"
echo ""
echo "MQTT over WebSocket SSL (port 8084):"
echo "  # Use wss://localhost:8084/mqtt in your WebSocket client"
echo ""

# Verify certificates
print_status "Verifying certificates..."
if openssl verify -CAfile "$CERT_DIR/cacert.pem" "$CERT_DIR/cert.pem" > /dev/null 2>&1; then
    print_success "Server certificate verification: OK"
else
    print_error "Server certificate verification: FAILED"
fi

if openssl verify -CAfile "$CERT_DIR/cacert.pem" "$CERT_DIR/client-cert.pem" > /dev/null 2>&1; then
    print_success "Client certificate verification: OK"
else
    print_error "Client certificate verification: FAILED"
fi

print_success "🎉 SSL certificate generation complete!"
