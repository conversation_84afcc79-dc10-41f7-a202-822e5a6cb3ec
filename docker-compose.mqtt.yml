# MQTT Broker Setup with EMQX
services:
  # EMQX MQTT Broker
  emqx:
    image: emqx/emqx:5.9
    container_name: gomama_emqx
    restart: unless-stopped
    ports:
      - "1883:1883"    # MQTT port
      - "8883:8883"    # MQTT SSL port
      - "34883:8883"   # Host port 34883 mapped to container's 8883 (SSL)
      - "8083:8083"    # MQTT over WebSocket
      - "8084:8084"    # MQTT over WebSocket SSL
      - "18083:18083"  # Dashboard
    volumes:
      - ./emqx/data:/opt/emqx/data
      - ./emqx/log:/opt/emqx/log
      - ./emqx/emqx.conf:/opt/emqx/etc/emqx.conf
      - ./emqx/auth-built-in-db-bootstrap.csv:/opt/emqx/etc/auth-built-in-db-bootstrap.csv
      # - ./certs:/etc/emqx/certs:ro
    networks:
      - gomama_mqtt
      - gomama
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx", "ctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis (keep for data storage)
  redis:
    image: redis:7-alpine
    container_name: gomama_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy noeviction
    volumes:
      - ./redis/data:/data
    networks:
      - gomama_mqtt
      - gomama
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Realtime service (updated for MQTT)
  gomama_realtime:
    container_name: gomama_realtime
    build: .
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # MQTT Configuration
      MQTT_BROKER_HOST: emqx
      MQTT_BROKER_PORT: 1883
      MQTT_CLIENT_ID: gomama_realtime_${HOSTNAME:-server}
      MQTT_USERNAME: ${MQTT_USERNAME:-realtime_client}
      MQTT_PASSWORD: ${MQTT_PASSWORD:-realtime_pass}

      # Redis Configuration  
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "9001:9001"  # Keep HTTP metrics endpoint
    depends_on:
      emqx:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - gomama_mqtt
      - gomama
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  gomama_mqtt:
    driver: bridge
    name: gomama_mqtt
  gomama:
    external: true

volumes:
  emqx_data:
  emqx_log:
  # emqx_etc:
  redis_data:
